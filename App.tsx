import Tts from "react-native-tts";
import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  ViroAnchorFoundMap,
  ViroARSceneNavigator,
} from "@reactvision/react-viro";
import {
  View,
  Text,
  Modal,
  Button,
  Platform,
  StyleSheet,
  TouchableOpacity,
  PermissionsAndroid,
} from "react-native";

import { DogSceneAR } from "./dog-scene-ar";
import { DuckSceneAR } from "./duck-scene-ar";
import { AppleSceneAR_GLB } from "./apple-scene-ar-glb";

const content =
  "Apples float in water because they’re 25% air. There are over 7,500 different varieties of apples. The skin contains most of the fiber and antioxidants!";

const ARExperienceScreen = () => {
  const arNavigatorRef = useRef<ViroARSceneNavigator | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    requestCameraPermission();
  }, []);

  const requestCameraPermission = async () => {
    if (Platform.OS === "android") {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.CAMERA,
        {
          title: "Camera Permission",
          message: "App needs camera access for AR scanning.",
          buttonNeutral: "Ask Me Later",
          buttonNegative: "Cancel",
          buttonPositive: "OK",
        }
      );
      setHasPermission(granted === PermissionsAndroid.RESULTS.GRANTED);
    } else {
      setHasPermission(true);
    }
  };

  const handleMarkerFound = useCallback(
    (anchorFoundMap: ViroAnchorFoundMap) => {
      console.log(JSON.stringify(anchorFoundMap, null, 4));
      setModalVisible(true);
    },
    []
  );

  const handleSpeak = useCallback(() => {
    Tts.stop();
    Tts.speak(content);
  }, []);

  if (!hasPermission) {
    return (
      <View style={styles.centered}>
        <Text>Camera permission is required.</Text>
        <Button title="Allow Camera" onPress={requestCameraPermission} />
      </View>
    );
  }

  return (
    <>
      <ViroARSceneNavigator
        ref={arNavigatorRef}
        initialScene={{
          scene: () => (
            <AppleSceneAR_GLB
              arNavigatorRef={arNavigatorRef}
              onFoundAnchor={handleMarkerFound}
            />
          ),
        }}
        autofocus
        worldAlignment="Gravity"
      />

      <Modal visible={modalVisible} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalText}>{content}</Text>

            <TouchableOpacity style={styles.speakButton} onPress={handleSpeak}>
              <Text style={styles.speakButtonText}>🔊 Speak</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
};

export default ARExperienceScreen;

const styles = StyleSheet.create({
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  modalBackdrop: {
    flex: 1,
    backgroundColor: "transparent",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  modalContainer: {
    backgroundColor: "#fff",
    padding: 20,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    alignItems: "center",
    width: "100%",
  },
  modalText: {
    fontSize: 16,
    color: "#000",
    marginBottom: 12,
    textAlign: "center",
  },
  speakButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  speakButtonText: {
    color: "#fff",
    fontSize: 16,
  },
  closeButton: {
    marginTop: 15,
  },
  closeButtonText: {
    color: "red",
    fontSize: 14,
  },
});
