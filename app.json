{"expo": {"name": "expo-starter-kit-typescript", "slug": "expo-starter-kit-typescript", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "newArchEnabled": false, "ios": {"supportsTablet": true, "infoPlist": {"NSPhotoLibraryUsageDescription": "$(PRODUCT_NAME) would like to read photos for AR experiences. This is a custom InfoPlist string!", "NSPhotoLibraryAddUsageDescription": "Allow $(PRODUCT_NAME) to save photos", "NSCameraUsageDescription": "$(PRODUCT_NAME) uses your camera for AR experiences. This is a custom InfoPlist string!", "NSMicrophoneUsageDescription": "$(PRODUCT_NAME) uses your microphone for AR experiences. This is a custom InfoPlist string!"}, "bundleIdentifier": "com.shoaibarifreactnativedev.expostarterkittypescript"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.shoaibarifreactnativedev.expostarterkittypescript"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["@reactvision/react-viro", {"android": {"xRMode": ["GVR", "AR", "OVR_MOBILE"]}, "ios": {"cameraUsagePermission": "$(PRODUCT_NAME) uses your camera for AR experiences. This is a custom InfoPlist string!", "microphoneUsagePermission": "$(PRODUCT_NAME) uses your microphone for AR experiences. This is a custom InfoPlist string!", "photosPermission": "$(PRODUCT_NAME) would like to read photos for AR experiences. This is a custom InfoPlist string!", "savephotosPermission": "$(PRODUCT_NAME) would like to save photos to your library during AR experiences. This is a custom InfoPlist string!"}}]], "extra": {"eas": {"projectId": "ff5a9d3d-a46f-4b83-8d8f-ed171e5ab022"}}, "owner": "shoaibarifreactnativedev"}}