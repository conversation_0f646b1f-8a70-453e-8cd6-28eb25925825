import React, { useState, useCallback } from "react";
import {
  ViroARScene,
  ViroAmbientLight,
  ViroDirectionalLight,
  Viro3DObject,
  ViroARImageMarker,
  ViroARTrackingTargets,
  ViroAnimations,
  ViroARSceneNavigator,
  ViroAnchorFoundMap,
} from "@reactvision/react-viro";
// 🔁 Register tracking targets
ViroARTrackingTargets.createTargets({
  logo: {
    source: require("./res/trigger-duck.png"),
    orientation: "Up",
    physicalWidth: 0.1, // meters
  },
});
ViroAnimations.registerAnimations({
  scaleCar: {
    properties: { scaleX: 0.1, scaleY: 0.1, scaleZ: 0.1 },
    duration: 500,
    easing: "bounce",
  },
});
type ARSceneProps = {
  arNavigatorRef: React.RefObject<ViroARSceneNavigator>;
  onFoundAnchor: (anchorFoundMap: ViroAnchorFoundMap) => void;
};

export const DuckSceneAR: React.FC<ARSceneProps> = ({ onFoundAnchor }) => {
  const _onAnchorFound = useCallback(
    async (anchorFoundMap: ViroAnchorFoundMap) => {
      onFoundAnchor(anchorFoundMap);
    },
    []
  );
  return (
    <ViroARScene>
      <ViroAmbientLight color="#ffffff" intensity={300} />
      <ViroDirectionalLight
        color="#ffffff"
        direction={[0, -1, -0.3]}
        intensity={500}
        castsShadow={true}
      />
      <ViroARImageMarker target="logo" onAnchorFound={_onAnchorFound}>
        <Viro3DObject
          source={require("./res/duck_obj.obj")} // ➊ the mesh
          resources={[require("./res/duck_mtl.mtl")]} // ➋ the material definitions
          type="OBJ"
          // position={[0, 0, -1]} // not sure you how you have configured your origin - set this so it renders the duck flat on top of the trigger image
          // scale={[1, 1, 1]} // set this so it renders the duck with a normal size on top of the trigger image
          // animation={{ name: "scaleCar", run: animateCar }}
          position={[0, 0, -0.1]} // adjust for proper placement
          scale={[-3, -3, -3]} // ✅ scale down the duck
        />
      </ViroARImageMarker>
    </ViroARScene>
  );
};
