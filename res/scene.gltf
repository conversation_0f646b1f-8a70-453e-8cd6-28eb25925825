{"accessors": [{"bufferView": 3, "componentType": 5126, "count": 1597, "max": [6.112865924835205, 22.206741333007812, 14.042037963867188], "min": [-6.112865924835205, 0.38205909729003906, -9.438480377197266], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 19164, "componentType": 5126, "count": 1597, "max": [0.9994564056396484, 0.9995179176330566, 0.9979496598243713], "min": [-0.9994564056396484, -0.9997987151145935, -0.9999969005584717], "type": "VEC3"}, {"bufferView": 2, "componentType": 5126, "count": 1597, "max": [0.99609375, 0.9888229370117188], "min": [0.00649261474609375, 0.00927734375], "type": "VEC2"}, {"bufferView": 1, "componentType": 5125, "count": 7734, "type": "SCALAR"}, {"bufferView": 5, "componentType": 5126, "count": 48, "max": [1.0, 0.9174267649650574, 1.0, 0.0, 1.000000238418579, 1.0, 0.4467903673648834, 0.0, 0.9745960235595703, 0.9991210699081421, 1.0, 0.0, 15.312192916870117, 20.980417251586914, 7.997713565826416, 1.0], "min": [-0.5326756834983826, -0.46209898591041565, -1.0000001192092896, 0.0, -1.0000001192092896, -0.9994786381721497, -0.44679000973701477, 0.0, -0.9994786977767944, -0.9991210699081421, -0.9923437237739563, 0.0, -18.294828414916992, -12.466368675231934, -7.99771785736084, 1.0], "type": "MAT4"}, {"bufferView": 0, "componentType": 5123, "count": 1597, "type": "VEC4"}, {"bufferView": 4, "componentType": 5126, "count": 1597, "max": [1.0, 0.49803924560546875, 0.3019607961177826, 0.1921568661928177], "min": [0.3176470696926117, 0.0, 0.0, 0.0], "type": "VEC4"}, {"bufferView": 6, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "componentType": 5126, "count": 14, "max": [0.5600757598876953, 2.6305060386657715, 9.5367431640625e-07], "min": [0.5600748062133789, 2.630504846572876, 2.384185791015625e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 56, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 168, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999999403953552, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 112, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "componentType": 5126, "count": 14, "max": [4.163336342344337e-17, 1.734723475976807e-18, 3.4694473655439205e-18, 1.0], "min": [-4.163336673216582e-17, -5.551115123125783e-17, -1.3877787807814457e-16, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 168, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 336, "componentType": 5126, "count": 14, "max": [2.8694076538085938, 4.76837158203125e-07, 4.76837158203125e-07], "min": [2.8694067001342773, -9.5367431640625e-07, -4.76837158203125e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 224, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 504, "componentType": 5126, "count": 14, "max": [1.0000003576278687, 1.000000238418579, 1.000000238418579], "min": [1.0, 1.0, 1.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 280, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 224, "componentType": 5126, "count": 14, "max": [0.9765712022781372, 0.660655677318573, 0.10518104583024979, 0.10117028653621674], "min": [0.7505369186401367, 0.15814627707004547, 0.011341569013893604, 0.009988874197006226], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 336, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 672, "componentType": 5126, "count": 14, "max": [3.891495943069458, 8.881784197001252e-16, 4.76837158203125e-07], "min": [3.8914947509765625, -9.5367431640625e-07, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 392, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 840, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998807907104, 0.9999997615814209, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 448, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 448, "componentType": 5126, "count": 14, "max": [0.873832106590271, 0.9098923802375793, 0.10472583025693893, 0.048483315855264664], "min": [0.41014134883880615, 0.4798875153064728, -0.04739657789468765, -0.19763068854808807], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 504, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 1008, "componentType": 5126, "count": 14, "max": [3.9782466888427734, 4.76837158203125e-07, -1.1920928955078125e-07], "min": [3.9782447814941406, -9.5367431640625e-07, -7.152557373046875e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 560, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 1176, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0], "min": [0.9999998211860657, 0.9999998211860657, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 616, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 672, "componentType": 5126, "count": 14, "max": [3.355087585532601e-08, 5.660250934624855e-08, -7.450580596923828e-08, 1.0], "min": [-3.650024282819686e-08, -4.6142979215346713e-08, -0.8264753818511963, 0.5629728436470032], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 672, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 1344, "componentType": 5126, "count": 14, "max": [-1.1963599920272827, 3.1197214126586914, -3.119166851043701], "min": [-1.196360468864441, 3.119719982147217, -3.1191673278808594], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 728, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 1512, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999999403953552, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 784, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 896, "componentType": 5126, "count": 14, "max": [0.9855839014053345, 0.9426727890968323, -0.005362759344279766, -0.024969205260276794], "min": [0.3305702805519104, -0.04167196527123451, -0.16795215010643005, -0.1303316056728363], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 840, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 1680, "componentType": 5126, "count": 14, "max": [1.3975830078125, 2.384185791015625e-07, 7.450580596923828e-09], "min": [1.3975811004638672, -9.5367431640625e-07, -3.725290298461914e-09], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 896, "componentType": 5126, "count": 12, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 1848, "componentType": 5126, "count": 12, "max": [1.0, 1.0, 1.0000001192092896], "min": [0.9999999403953552, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 944, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 1120, "componentType": 5126, "count": 14, "max": [1.1691655456280703e-11, 1.5935139430212075e-08, 0.017474837601184845, 0.9998472929000854], "min": [-1.6577846508880612e-06, -1.3157160694277081e-08, 0.01747482270002365, 0.9998472929000854], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 1000, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 1992, "componentType": 5126, "count": 14, "max": [0.9856829643249512, 9.5367431640625e-07, 2.9802322387695312e-08], "min": [0.9856815338134766, -4.76837158203125e-07, -1.4901161193847656e-08], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1056, "componentType": 5126, "count": 13, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 2160, "componentType": 5126, "count": 13, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998807907104, 0.9999998211860657, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1108, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 1344, "componentType": 5126, "count": 14, "max": [8.880401969335594e-11, 0.0012034771498292685, -0.15284815430641174, 0.9882415533065796], "min": [-0.003822206286713481, -1.1173844072231987e-08, -0.49046754837036133, 0.8714594841003418], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 1164, "componentType": 5126, "count": 13, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 2316, "componentType": 5126, "count": 13, "max": [-5.082709312438965, 4.322815418243408, -2.086162567138672e-07], "min": [-5.082710266113281, 4.322813987731934, -2.5331974029541016e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1216, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 2472, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.000000238418579, 1.0000001192092896], "min": [0.9999999403953552, 0.9999999403953552, 1.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1272, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 1568, "componentType": 5126, "count": 14, "max": [0.10710550099611282, 0.9999913573265076, 0.002355457516387105, 0.00024413444043602794], "min": [-0.24920491874217987, 0.9684507846832275, -6.8399148567266366e-09, -0.0032480834051966667], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 1328, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 2640, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999999403953552, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1384, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 1792, "componentType": 5126, "count": 14, "max": [6.938893903907228e-18, 3.4694473655439205e-18, 5.5511144613812927e-17, 1.0], "min": [-1.0249488013869268e-07, -1.7347236827719602e-18, -5.551115123125783e-17, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 1440, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 2808, "componentType": 5126, "count": 14, "max": [1.3255362510681152, 3.319889545440674, 0.0], "min": [1.3255348205566406, 3.319887161254883, -3.5762786865234375e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1496, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 2976, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998211860657, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1552, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 2016, "componentType": 5126, "count": 14, "max": [6.938894731087841e-18, 3.469446538363308e-18, 2.7755575615628914e-17, 1.0], "min": [-2.9799340950376063e-07, -1.0408338374319005e-17, -5.551115784870273e-17, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 1608, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 3144, "componentType": 5126, "count": 14, "max": [2.263310432434082, 0.0, 1.1920928955078125e-07], "min": [2.2633092403411865, -9.5367431640625e-07, -3.5762786865234375e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1664, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 3312, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.000000238418579, 1.0000003576278687], "min": [0.9999998211860657, 0.9999998807907104, 0.9999998211860657], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1720, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 2240, "componentType": 5126, "count": 14, "max": [0.9727485775947571, 0.6054431200027466, -0.042849671095609665, 0.08795052021741867], "min": [0.7890164852142334, 0.20670448243618011, -0.10149650275707245, -0.02429043874144554], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 1776, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 3480, "componentType": 5126, "count": 14, "max": [2.4375476837158203, 9.5367431640625e-07, 4.76837158203125e-07], "min": [2.437546730041504, -1.430511474609375e-06, 2.220446049250313e-16], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1832, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 3648, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0], "min": [0.9999998807907104, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1888, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 2464, "componentType": 5126, "count": 14, "max": [2.5757518784530475e-08, 1.5997638769249534e-08, -1.7881393432617188e-07, 1.0], "min": [-1.3186538581066998e-06, -4.88587765801185e-08, -0.7853699326515198, 0.6190267205238342], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 1944, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 3816, "componentType": 5126, "count": 14, "max": [2.528494358062744, 4.76837158203125e-07, 4.76837158203125e-07], "min": [2.5284924507141113, -9.5367431640625e-07, -4.76837158203125e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2000, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 3984, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.000000238418579, 1.0000001192092896], "min": [0.9999998807907104, 0.9999998211860657, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2056, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 2688, "componentType": 5126, "count": 14, "max": [0.26985281705856323, -0.1938825100660324, 0.5412552952766418, 0.935198962688446], "min": [0.049525726586580276, -0.3559049665927887, 0.137358620762825, 0.7919927835464478], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 2112, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 4152, "componentType": 5126, "count": 14, "max": [0.3397202491760254, 2.971212387084961, -1.1939908266067505], "min": [-0.1502971649169922, 1.7434558868408203, -1.2537816762924194], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2168, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 4320, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.000000238418579, 1.0000001192092896], "min": [0.9999999403953552, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2224, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 2912, "componentType": 5126, "count": 14, "max": [-0.21990950405597687, 0.051357220858335495, 0.6732922196388245, 0.8917532563209534], "min": [-0.2676522731781006, -0.08811599016189575, 0.3943210244178772, 0.6978573799133301], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 2280, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 4488, "componentType": 5126, "count": 14, "max": [0.735630989074707, 3.552713678800501e-15, 7.450580596923828e-08], "min": [0.735628604888916, -1.9073486328125e-06, 5.587935447692871e-08], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2336, "componentType": 5126, "count": 11, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 4656, "componentType": 5126, "count": 11, "max": [1.0, 1.0, 1.0], "min": [0.9999998807907104, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2380, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 3136, "componentType": 5126, "count": 14, "max": [-2.3305801732931286e-10, 2.2817399525365545e-08, 8.858450556886764e-17, 1.0], "min": [-1.7179211226903135e-06, -7.117135402685614e-19, -5.551115123125783e-17, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 2436, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 4788, "componentType": 5126, "count": 14, "max": [2.666889190673828, 4.665970325469971, 5.551115123125783e-17], "min": [2.6668853759765625, 4.665966987609863, -2.9802322387695312e-08], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2492, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 4956, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [1.0, 1.0, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2548, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 3360, "componentType": 5126, "count": 14, "max": [2.980244317996039e-08, 4.465229253014513e-08, 0.6966385245323181, 0.8700879812240601], "min": [-3.0220761004784435e-08, -2.989500913486154e-08, 0.4928964674472809, 0.7174223065376282], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 2604, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 5124, "componentType": 5126, "count": 14, "max": [1.8285446166992188, 9.5367431640625e-07, 1.9073486328125e-06], "min": [1.828542709350586, -4.76837158203125e-07, -9.5367431640625e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2660, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 5292, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.000000238418579], "min": [0.9999999403953552, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2716, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 3584, "componentType": 5126, "count": 14, "max": [5.452306606201773e-09, 4.6560209021606624e-09, 3.122502256758253e-17, 1.0], "min": [-1.3104501022098702e-06, -5.551115123125783e-17, -5.2147431262028476e-08, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 2772, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 5460, "componentType": 5126, "count": 14, "max": [2.435553550720215, 0.0, 1.9073486328125e-06], "min": [2.4355506896972656, -1.1920928955078125e-06, -1.9073486328125e-06], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2828, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 5628, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.0000001192092896, 1.000000238418579], "min": [0.9999999403953552, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2884, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 3808, "componentType": 5126, "count": 14, "max": [-0.008874062448740005, -0.02342088147997856, 0.0224038977175951, 0.9994352459907532], "min": [-0.008875356055796146, -0.02342091128230095, 0.022403854876756668, 0.9994352459907532], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 2940, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 5796, "componentType": 5126, "count": 14, "max": [2.5022010803222656, 4.76837158203125e-07, 9.5367431640625e-07], "min": [2.5021982192993164, 0.0, -1.9073486328125e-06], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2996, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 5964, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.0000001192092896, 1.000000238418579], "min": [0.9999999403953552, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3052, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 4032, "componentType": 5126, "count": 14, "max": [-0.010815772227942944, -0.035287417471408844, 0.11629112809896469, 0.9971797466278076], "min": [-0.012649684213101864, -0.03589199483394623, 0.06502848863601685, 0.9925075173377991], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 3108, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 6132, "componentType": 5126, "count": 14, "max": [0.7918529510498047, -0.07079124450683594, -2.8079183101654053], "min": [0.7918491363525391, -0.07079267501831055, -2.8079190254211426], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3164, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 6300, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.0000001192092896, 1.0000001192092896], "min": [1.0, 0.9999998807907104, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3220, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 4256, "componentType": 5126, "count": 14, "max": [0.5825074911117554, 0.6719229221343994, 0.31538236141204834, 0.5268208384513855], "min": [-0.5830988883972168, -0.5598363876342773, -0.38996782898902893, -0.4977973401546478], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 3276, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 6468, "componentType": 5126, "count": 14, "max": [3.748281478881836, 4.76837158203125e-07, 1.2293457984924316e-07], "min": [3.7482776641845703, -1.9073486328125e-06, 8.940696716308594e-08], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3332, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 6636, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999999403953552, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3388, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 4480, "componentType": 5126, "count": 14, "max": [1.2686314221355133e-09, 1.2599656449438345e-18, 5.551115784870273e-17, 1.0], "min": [-1.2024902389384806e-06, -2.9333536488707068e-08, -7.449786121327406e-09, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 3444, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 6804, "componentType": 5126, "count": 14, "max": [-1.828542709350586, 4.76837158203125e-07, 1.9073486328125e-06], "min": [-1.8285446166992188, -9.5367431640625e-07, -1.9073486328125e-06], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3500, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 6972, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998807907104, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3556, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 4704, "componentType": 5126, "count": 14, "max": [-1.3877786153453232e-17, 5.551124387548643e-17, 7.450479788673192e-09, 1.0], "min": [-1.7819586446421454e-06, -1.8626198361459956e-09, -4.857216137439954e-17, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 3612, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 7140, "componentType": 5126, "count": 14, "max": [-2.4355506896972656, 9.5367431640625e-07, 1.7763568394002505e-15], "min": [-2.435553550720215, -4.76837158203125e-07, -1.9073486328125e-06], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3668, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 7308, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998807907104, 1.0, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3724, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 4928, "componentType": 5126, "count": 14, "max": [-0.008874062448740005, -0.023420855402946472, 0.022403936833143234, 0.9994352459907532], "min": [-0.00887578260153532, -0.023420896381139755, 0.022403890267014503, 0.9994351863861084], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 3780, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 7476, "componentType": 5126, "count": 14, "max": [-2.5021982192993164, 1.1920928955078125e-07, 1.9073486328125e-06], "min": [-2.502200126647949, -9.5367431640625e-07, -9.5367431640625e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3836, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 7644, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.000000238418579, 1.000000238418579], "min": [0.9999999403953552, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3892, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 5152, "componentType": 5126, "count": 14, "max": [-0.010815797373652458, -0.03528743237257004, 0.11629113554954529, 0.9971797466278076], "min": [-0.0126497158780694, -0.035891976207494736, 0.06502851098775864, 0.9925075173377991], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 3948, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 7812, "componentType": 5126, "count": 14, "max": [0.7918510437011719, -0.07079124450683594, 2.8079190254211426], "min": [0.7918491363525391, -0.07079362869262695, 2.8079183101654053], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4004, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 7980, "componentType": 5126, "count": 14, "max": [1.0000003576278687, 1.000000238418579, 1.000000238418579], "min": [1.0, 0.9999999403953552, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4060, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 5376, "componentType": 5126, "count": 14, "max": [0.6719228029251099, 0.5830989480018616, -0.4504930078983307, -0.24002516269683838], "min": [0.48143044114112854, 0.5366224646568298, -0.5268210172653198, -0.38996800780296326], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 4116, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 8148, "componentType": 5126, "count": 14, "max": [2.032137870788574, 1.9073486328125e-06, 6.332993507385254e-08], "min": [2.032135009765625, -4.76837158203125e-07, 2.9802322387695312e-08], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4172, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 8316, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999999403953552, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4228, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 5600, "componentType": 5126, "count": 14, "max": [8.457589419389941e-14, 3.6068801652279926e-15, -0.12689174711704254, 0.9919165968894958], "min": [-1.0070621101476718e-06, -1.2882925659596367e-07, -0.12689176201820374, 0.9919165968894958], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 4284, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 8484, "componentType": 5126, "count": 14, "max": [2.994600296020508, 1.7763568394002505e-15, -2.9802322387695312e-08], "min": [2.994598388671875, -1.430511474609375e-06, -7.450580596923828e-08], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4340, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 8652, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.000000238418579, 1.0000001192092896], "min": [0.9999998807907104, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4396, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 5824, "componentType": 5126, "count": 14, "max": [2.991466274693266e-08, 5.961638294138538e-08, 0.049333907663822174, 0.9999397993087769], "min": [-1.4508636070331704e-08, -2.980232949312267e-08, -0.17143189907073975, 0.9851959943771362], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 4452, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 8820, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998807907104, 0.9999998211860657, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4508, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 6048, "componentType": 5126, "count": 14, "max": [1.9024355424335226e-09, 1.0408153913042406e-17, 7.450799976993494e-09, 1.0], "min": [-1.349686272078543e-06, -1.1641873243206646e-08, -2.7755578924351364e-17, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 4564, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 8988, "componentType": 5126, "count": 14, "max": [-1.3255348205566406, -3.319888114929199, -1.7881393432617188e-07], "min": [-1.3255362510681152, -3.3198904991149902, -3.5762786865234375e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4620, "componentType": 5126, "count": 13, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 9156, "componentType": 5126, "count": 13, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998807907104, 0.9999998807907104, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4672, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 6272, "componentType": 5126, "count": 14, "max": [9.62964972193618e-35, 6.938893076726616e-18, 1.490100398626737e-08, 1.0], "min": [-1.3506718232747517e-06, -1.0710096809418701e-08, -2.7755575615628914e-17, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 4728, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 9312, "componentType": 5126, "count": 14, "max": [-2.2633092403411865, 4.76837158203125e-07, 3.5762786865234375e-07], "min": [-2.263310194015503, -9.5367431640625e-07, 1.1920928955078125e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4784, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 9480, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.000000238418579, 1.0000003576278687], "min": [0.9999998807907104, 0.9999998807907104, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4840, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 6496, "componentType": 5126, "count": 14, "max": [0.994645357131958, 0.6807812452316284, -0.03430912271142006, -0.00042286908137612045], "min": [0.7234923839569092, 0.08340335637331009, -0.10305988043546677, -0.08003189414739609], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 4896, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 9648, "componentType": 5126, "count": 14, "max": [-2.437546730041504, 0.0, 4.172325134277344e-07], "min": [-2.4375481605529785, -9.5367431640625e-07, 5.960464477539063e-08], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4952, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 9816, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0], "min": [0.9999998807907104, 0.9999998807907104, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5008, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 6720, "componentType": 5126, "count": 14, "max": [1.754046330404435e-08, 5.736562869174122e-08, -2.384185791015625e-07, 1.0], "min": [-5.180941116122995e-07, -5.011845516378344e-08, -0.6744205951690674, 0.7383474111557007], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 5064, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 9984, "componentType": 5126, "count": 14, "max": [-2.5284929275512695, 9.5367431640625e-07, 4.76837158203125e-07], "min": [-2.5284948348999023, -9.5367431640625e-07, -4.76837158203125e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5120, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 10152, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.000000238418579, 1.0000001192092896], "min": [0.9999998807907104, 0.9999998807907104, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5176, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 6944, "componentType": 5126, "count": 14, "max": [0.24938589334487915, -0.20173326134681702, 0.6284528374671936, 0.9400849938392639], "min": [0.046974219381809235, -0.28579193353652954, 0.13851676881313324, 0.7235280871391296], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 5232, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 10320, "componentType": 5126, "count": 14, "max": [-0.0026340484619140625, 2.491990089416504, 1.2537819147109985], "min": [-0.1502971649169922, 1.7434558868408203, 1.2262089252471924], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5288, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 10488, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.000000238418579, 1.0000001192092896], "min": [0.9999998211860657, 0.9999998807907104, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5344, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 7168, "componentType": 5126, "count": 14, "max": [0.03524519130587578, 0.2749654948711395, 0.8490003943443298, 0.7265025973320007], "min": [-0.10134164243936539, -0.22888624668121338, -0.8085549473762512, -0.4847089350223541], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 5400, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 10656, "componentType": 5126, "count": 14, "max": [2.9497556686401367, 9.5367431640625e-07, 1.4901161193847656e-08], "min": [2.949754238128662, -9.5367431640625e-07, -2.9802322387695312e-08], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5456, "componentType": 5126, "count": 12, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 10824, "componentType": 5126, "count": 12, "max": [1.0000001192092896, 1.000000238418579, 1.0000001192092896], "min": [0.9999999403953552, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5504, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 7392, "componentType": 5126, "count": 14, "max": [3.0457798061434005e-08, 4.4722060721369417e-08, -0.11013327538967133, 0.9939168095588684], "min": [-3.037547813278252e-08, -5.981225115192501e-08, -0.21224182844161987, 0.9772171974182129], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 5560, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 10968, "componentType": 5126, "count": 14, "max": [2.0891637802124023, 1.9073486328125e-06, 1.4901161193847656e-08], "min": [2.089163064956665, -9.5367431640625e-07, -2.9802322387695312e-08], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5616, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 11136, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998807907104, 0.9999999403953552, 1.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5672, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 7616, "componentType": 5126, "count": 14, "max": [2.9359643605175734e-08, 2.981034796789572e-08, 0.13872522115707397, 0.9993641376495361], "min": [-2.9652028388227336e-08, -2.9991934269446574e-08, 0.035655226558446884, 0.9903309345245361], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 5728, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 11304, "componentType": 5126, "count": 14, "max": [1.5796539783477783, 9.5367431640625e-07, 7.450580596923828e-09], "min": [1.579653263092041, -9.5367431640625e-07, -1.1368683772161603e-13], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5784, "componentType": 5126, "count": 13, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 11472, "componentType": 5126, "count": 13, "max": [1.000000238418579, 1.0000001192092896, 1.0000001192092896], "min": [0.9999999403953552, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5836, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 7840, "componentType": 5126, "count": 14, "max": [5.950363046736129e-08, 3.003589554850805e-08, 0.1548513025045395, 0.9999129772186279], "min": [-0.0007466379320248961, -0.0044003133662045, -0.10718382149934769, 0.9879378080368042], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 5892, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 11628, "componentType": 5126, "count": 14, "max": [-0.5600748062133789, -2.630504846572876, 4.76837158203125e-07], "min": [-0.5600757598876953, -2.6305055618286133, -9.5367431640625e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5948, "componentType": 5126, "count": 13, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 11796, "componentType": 5126, "count": 13, "max": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998807907104, 0.9999998807907104, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6000, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 8064, "componentType": 5126, "count": 14, "max": [2.7755575615628914e-17, 3.4694291675704443e-18, 7.449683536719931e-09, 1.0], "min": [-1.2794914709957084e-06, -1.024369922220103e-08, -2.7755582233073814e-17, 1.0], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 6056, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 11952, "componentType": 5126, "count": 14, "max": [-2.8694064617156982, 8.881784197001252e-16, 4.76837158203125e-07], "min": [-2.8694074153900146, -9.5367431640625e-07, -4.76837158203125e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6112, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 12120, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.000000238418579, 1.000000238418579], "min": [0.9999998807907104, 0.9999999403953552, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6168, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 8288, "componentType": 5126, "count": 14, "max": [0.9732587337493896, 0.6606556177139282, 0.11552143841981888, 0.10970555245876312], "min": [0.7505369186401367, 0.1654900312423706, 0.011341540142893791, 0.009982885792851448], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 6224, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 12288, "componentType": 5126, "count": 14, "max": [-3.8914947509765625, 4.76837158203125e-07, 4.76837158203125e-07], "min": [-3.891495704650879, -4.76837158203125e-07, -4.76837158203125e-07], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6280, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 12456, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.000000238418579, 1.000000238418579], "min": [0.9999998211860657, 0.9999998211860657, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6336, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 8512, "componentType": 5126, "count": 14, "max": [0.8518930673599243, 0.9098315238952637, 0.12718860805034637, 0.06771555542945862], "min": [0.41023707389831543, 0.518802285194397, -0.04906714707612991, -0.2003723382949829], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 6392, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 12624, "componentType": 5126, "count": 14, "max": [-3.978245258331299, 4.76837158203125e-07, 7.152557373046875e-07], "min": [-3.9782466888427734, -5.960464477539063e-08, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6448, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 12792, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998807907104, 1.0, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6504, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 8736, "componentType": 5126, "count": 14, "max": [4.1034642350723516e-08, 4.555563748454006e-08, -7.450581307466564e-08, 1.0], "min": [-6.8502664873904e-08, -3.402180581701941e-08, -0.817077100276947, 0.5765284299850464], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 6560, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 12960, "componentType": 5126, "count": 14, "max": [-1.1963591575622559, 3.1197214126586914, 3.1191670894622803], "min": [-1.1963601112365723, 3.119720458984375, 3.119166374206543], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6616, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 13128, "componentType": 5126, "count": 14, "max": [1.000000238418579, 1.000000238418579, 1.0000001192092896], "min": [0.9999998807907104, 0.9999999403953552, 0.9999998211860657], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6672, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 8960, "componentType": 5126, "count": 14, "max": [0.935204029083252, 0.9815986156463623, 0.05123266577720642, 0.18547996878623962], "min": [-0.6471502780914307, -0.5710114240646362, -0.16753119230270386, -0.09425728023052216], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 6728, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 13296, "componentType": 5126, "count": 14, "max": [5.2735593669694936e-15, 12.414107322692871, 0.7008303999900818], "min": [5.10702591327572e-15, 10.40308666229248, -0.409729927778244], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6784, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 7, "byteOffset": 13464, "componentType": 5126, "count": 14, "max": [1.0000001192092896, 1.000000238418579, 1.000000238418579], "min": [0.9999999403953552, 0.9999999403953552, 0.9999998807907104], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6840, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 9184, "componentType": 5126, "count": 14, "max": [0.7070965766906738, 0.0978877916932106, 0.7094321846961975, 0.09788816422224045], "min": [0.6810508370399475, -0.19018344581127167, 0.6774358153343201, -0.20268380641937256], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 6896, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 9408, "componentType": 5126, "count": 14, "max": [0.5891650319099426, -0.5430843830108643, 0.5781811475753784, -0.15378719568252563], "min": [0.589164674282074, -0.5430854558944702, 0.5781801342964172, -0.15378838777542114], "type": "VEC4"}, {"bufferView": 6, "byteOffset": 6952, "componentType": 5126, "count": 14, "max": [0.4333333373069763], "min": [0.0], "type": "SCALAR"}, {"bufferView": 8, "byteOffset": 9632, "componentType": 5126, "count": 14, "max": [0.5651280283927917, -0.43259197473526, -0.5246964693069458, 0.46710631251335144], "min": [0.5651271343231201, -0.4325929284095764, -0.5246973037719727, 0.4671051800251007], "type": "VEC4"}], "animations": [{"channels": [{"sampler": 0, "target": {"node": 14, "path": "translation"}}, {"sampler": 1, "target": {"node": 14, "path": "scale"}}, {"sampler": 2, "target": {"node": 14, "path": "rotation"}}, {"sampler": 3, "target": {"node": 13, "path": "translation"}}, {"sampler": 4, "target": {"node": 13, "path": "scale"}}, {"sampler": 5, "target": {"node": 13, "path": "rotation"}}, {"sampler": 6, "target": {"node": 12, "path": "translation"}}, {"sampler": 7, "target": {"node": 12, "path": "scale"}}, {"sampler": 8, "target": {"node": 12, "path": "rotation"}}, {"sampler": 9, "target": {"node": 11, "path": "translation"}}, {"sampler": 10, "target": {"node": 11, "path": "scale"}}, {"sampler": 11, "target": {"node": 11, "path": "rotation"}}, {"sampler": 12, "target": {"node": 10, "path": "translation"}}, {"sampler": 13, "target": {"node": 10, "path": "scale"}}, {"sampler": 14, "target": {"node": 10, "path": "rotation"}}, {"sampler": 15, "target": {"node": 17, "path": "translation"}}, {"sampler": 16, "target": {"node": 17, "path": "scale"}}, {"sampler": 17, "target": {"node": 17, "path": "rotation"}}, {"sampler": 18, "target": {"node": 16, "path": "translation"}}, {"sampler": 19, "target": {"node": 16, "path": "scale"}}, {"sampler": 20, "target": {"node": 16, "path": "rotation"}}, {"sampler": 21, "target": {"node": 15, "path": "translation"}}, {"sampler": 22, "target": {"node": 15, "path": "scale"}}, {"sampler": 23, "target": {"node": 15, "path": "rotation"}}, {"sampler": 24, "target": {"node": 26, "path": "scale"}}, {"sampler": 25, "target": {"node": 26, "path": "rotation"}}, {"sampler": 26, "target": {"node": 25, "path": "translation"}}, {"sampler": 27, "target": {"node": 25, "path": "scale"}}, {"sampler": 28, "target": {"node": 25, "path": "rotation"}}, {"sampler": 29, "target": {"node": 24, "path": "translation"}}, {"sampler": 30, "target": {"node": 24, "path": "scale"}}, {"sampler": 31, "target": {"node": 24, "path": "rotation"}}, {"sampler": 32, "target": {"node": 23, "path": "translation"}}, {"sampler": 33, "target": {"node": 23, "path": "scale"}}, {"sampler": 34, "target": {"node": 23, "path": "rotation"}}, {"sampler": 35, "target": {"node": 22, "path": "translation"}}, {"sampler": 36, "target": {"node": 22, "path": "scale"}}, {"sampler": 37, "target": {"node": 22, "path": "rotation"}}, {"sampler": 38, "target": {"node": 21, "path": "translation"}}, {"sampler": 39, "target": {"node": 21, "path": "scale"}}, {"sampler": 40, "target": {"node": 21, "path": "rotation"}}, {"sampler": 41, "target": {"node": 30, "path": "translation"}}, {"sampler": 42, "target": {"node": 30, "path": "scale"}}, {"sampler": 43, "target": {"node": 30, "path": "rotation"}}, {"sampler": 44, "target": {"node": 29, "path": "translation"}}, {"sampler": 45, "target": {"node": 29, "path": "scale"}}, {"sampler": 46, "target": {"node": 29, "path": "rotation"}}, {"sampler": 47, "target": {"node": 34, "path": "translation"}}, {"sampler": 48, "target": {"node": 34, "path": "scale"}}, {"sampler": 49, "target": {"node": 34, "path": "rotation"}}, {"sampler": 50, "target": {"node": 33, "path": "translation"}}, {"sampler": 51, "target": {"node": 33, "path": "scale"}}, {"sampler": 52, "target": {"node": 33, "path": "rotation"}}, {"sampler": 53, "target": {"node": 32, "path": "translation"}}, {"sampler": 54, "target": {"node": 32, "path": "scale"}}, {"sampler": 55, "target": {"node": 32, "path": "rotation"}}, {"sampler": 56, "target": {"node": 31, "path": "translation"}}, {"sampler": 57, "target": {"node": 31, "path": "scale"}}, {"sampler": 58, "target": {"node": 31, "path": "rotation"}}, {"sampler": 59, "target": {"node": 35, "path": "translation"}}, {"sampler": 60, "target": {"node": 35, "path": "scale"}}, {"sampler": 61, "target": {"node": 35, "path": "rotation"}}, {"sampler": 62, "target": {"node": 39, "path": "translation"}}, {"sampler": 63, "target": {"node": 39, "path": "scale"}}, {"sampler": 64, "target": {"node": 39, "path": "rotation"}}, {"sampler": 65, "target": {"node": 38, "path": "translation"}}, {"sampler": 66, "target": {"node": 38, "path": "scale"}}, {"sampler": 67, "target": {"node": 38, "path": "rotation"}}, {"sampler": 68, "target": {"node": 37, "path": "translation"}}, {"sampler": 69, "target": {"node": 37, "path": "scale"}}, {"sampler": 70, "target": {"node": 37, "path": "rotation"}}, {"sampler": 71, "target": {"node": 36, "path": "translation"}}, {"sampler": 72, "target": {"node": 36, "path": "scale"}}, {"sampler": 73, "target": {"node": 36, "path": "rotation"}}, {"sampler": 74, "target": {"node": 28, "path": "translation"}}, {"sampler": 75, "target": {"node": 28, "path": "scale"}}, {"sampler": 76, "target": {"node": 28, "path": "rotation"}}, {"sampler": 77, "target": {"node": 27, "path": "translation"}}, {"sampler": 78, "target": {"node": 27, "path": "scale"}}, {"sampler": 79, "target": {"node": 27, "path": "rotation"}}, {"sampler": 80, "target": {"node": 45, "path": "scale"}}, {"sampler": 81, "target": {"node": 45, "path": "rotation"}}, {"sampler": 82, "target": {"node": 44, "path": "translation"}}, {"sampler": 83, "target": {"node": 44, "path": "scale"}}, {"sampler": 84, "target": {"node": 44, "path": "rotation"}}, {"sampler": 85, "target": {"node": 43, "path": "translation"}}, {"sampler": 86, "target": {"node": 43, "path": "scale"}}, {"sampler": 87, "target": {"node": 43, "path": "rotation"}}, {"sampler": 88, "target": {"node": 42, "path": "translation"}}, {"sampler": 89, "target": {"node": 42, "path": "scale"}}, {"sampler": 90, "target": {"node": 42, "path": "rotation"}}, {"sampler": 91, "target": {"node": 41, "path": "translation"}}, {"sampler": 92, "target": {"node": 41, "path": "scale"}}, {"sampler": 93, "target": {"node": 41, "path": "rotation"}}, {"sampler": 94, "target": {"node": 40, "path": "translation"}}, {"sampler": 95, "target": {"node": 40, "path": "scale"}}, {"sampler": 96, "target": {"node": 40, "path": "rotation"}}, {"sampler": 97, "target": {"node": 20, "path": "translation"}}, {"sampler": 98, "target": {"node": 20, "path": "scale"}}, {"sampler": 99, "target": {"node": 20, "path": "rotation"}}, {"sampler": 100, "target": {"node": 19, "path": "translation"}}, {"sampler": 101, "target": {"node": 19, "path": "scale"}}, {"sampler": 102, "target": {"node": 19, "path": "rotation"}}, {"sampler": 103, "target": {"node": 18, "path": "translation"}}, {"sampler": 104, "target": {"node": 18, "path": "scale"}}, {"sampler": 105, "target": {"node": 18, "path": "rotation"}}, {"sampler": 106, "target": {"node": 50, "path": "translation"}}, {"sampler": 107, "target": {"node": 50, "path": "scale"}}, {"sampler": 108, "target": {"node": 50, "path": "rotation"}}, {"sampler": 109, "target": {"node": 49, "path": "translation"}}, {"sampler": 110, "target": {"node": 49, "path": "scale"}}, {"sampler": 111, "target": {"node": 49, "path": "rotation"}}, {"sampler": 112, "target": {"node": 48, "path": "translation"}}, {"sampler": 113, "target": {"node": 48, "path": "scale"}}, {"sampler": 114, "target": {"node": 48, "path": "rotation"}}, {"sampler": 115, "target": {"node": 47, "path": "translation"}}, {"sampler": 116, "target": {"node": 47, "path": "scale"}}, {"sampler": 117, "target": {"node": 47, "path": "rotation"}}, {"sampler": 118, "target": {"node": 46, "path": "translation"}}, {"sampler": 119, "target": {"node": 46, "path": "scale"}}, {"sampler": 120, "target": {"node": 46, "path": "rotation"}}, {"sampler": 121, "target": {"node": 9, "path": "translation"}}, {"sampler": 122, "target": {"node": 9, "path": "scale"}}, {"sampler": 123, "target": {"node": 9, "path": "rotation"}}, {"sampler": 124, "target": {"node": 52, "path": "rotation"}}, {"sampler": 125, "target": {"node": 54, "path": "rotation"}}], "name": "Take 001", "samplers": [{"input": 7, "interpolation": "LINEAR", "output": 8}, {"input": 9, "interpolation": "LINEAR", "output": 10}, {"input": 11, "interpolation": "LINEAR", "output": 12}, {"input": 13, "interpolation": "LINEAR", "output": 14}, {"input": 15, "interpolation": "LINEAR", "output": 16}, {"input": 17, "interpolation": "LINEAR", "output": 18}, {"input": 19, "interpolation": "LINEAR", "output": 20}, {"input": 21, "interpolation": "LINEAR", "output": 22}, {"input": 23, "interpolation": "LINEAR", "output": 24}, {"input": 25, "interpolation": "LINEAR", "output": 26}, {"input": 27, "interpolation": "LINEAR", "output": 28}, {"input": 29, "interpolation": "LINEAR", "output": 30}, {"input": 31, "interpolation": "LINEAR", "output": 32}, {"input": 33, "interpolation": "LINEAR", "output": 34}, {"input": 35, "interpolation": "LINEAR", "output": 36}, {"input": 37, "interpolation": "LINEAR", "output": 38}, {"input": 39, "interpolation": "LINEAR", "output": 40}, {"input": 41, "interpolation": "LINEAR", "output": 42}, {"input": 43, "interpolation": "LINEAR", "output": 44}, {"input": 45, "interpolation": "LINEAR", "output": 46}, {"input": 47, "interpolation": "LINEAR", "output": 48}, {"input": 49, "interpolation": "LINEAR", "output": 50}, {"input": 51, "interpolation": "LINEAR", "output": 52}, {"input": 53, "interpolation": "LINEAR", "output": 54}, {"input": 55, "interpolation": "LINEAR", "output": 56}, {"input": 57, "interpolation": "LINEAR", "output": 58}, {"input": 59, "interpolation": "LINEAR", "output": 60}, {"input": 61, "interpolation": "LINEAR", "output": 62}, {"input": 63, "interpolation": "LINEAR", "output": 64}, {"input": 65, "interpolation": "LINEAR", "output": 66}, {"input": 67, "interpolation": "LINEAR", "output": 68}, {"input": 69, "interpolation": "LINEAR", "output": 70}, {"input": 71, "interpolation": "LINEAR", "output": 72}, {"input": 73, "interpolation": "LINEAR", "output": 74}, {"input": 75, "interpolation": "LINEAR", "output": 76}, {"input": 77, "interpolation": "LINEAR", "output": 78}, {"input": 79, "interpolation": "LINEAR", "output": 80}, {"input": 81, "interpolation": "LINEAR", "output": 82}, {"input": 83, "interpolation": "LINEAR", "output": 84}, {"input": 85, "interpolation": "LINEAR", "output": 86}, {"input": 87, "interpolation": "LINEAR", "output": 88}, {"input": 89, "interpolation": "LINEAR", "output": 90}, {"input": 91, "interpolation": "LINEAR", "output": 92}, {"input": 93, "interpolation": "LINEAR", "output": 94}, {"input": 95, "interpolation": "LINEAR", "output": 96}, {"input": 97, "interpolation": "LINEAR", "output": 98}, {"input": 99, "interpolation": "LINEAR", "output": 100}, {"input": 101, "interpolation": "LINEAR", "output": 102}, {"input": 103, "interpolation": "LINEAR", "output": 104}, {"input": 105, "interpolation": "LINEAR", "output": 106}, {"input": 107, "interpolation": "LINEAR", "output": 108}, {"input": 109, "interpolation": "LINEAR", "output": 110}, {"input": 111, "interpolation": "LINEAR", "output": 112}, {"input": 113, "interpolation": "LINEAR", "output": 114}, {"input": 115, "interpolation": "LINEAR", "output": 116}, {"input": 117, "interpolation": "LINEAR", "output": 118}, {"input": 119, "interpolation": "LINEAR", "output": 120}, {"input": 121, "interpolation": "LINEAR", "output": 122}, {"input": 123, "interpolation": "LINEAR", "output": 124}, {"input": 125, "interpolation": "LINEAR", "output": 126}, {"input": 127, "interpolation": "LINEAR", "output": 128}, {"input": 129, "interpolation": "LINEAR", "output": 130}, {"input": 131, "interpolation": "LINEAR", "output": 132}, {"input": 133, "interpolation": "LINEAR", "output": 134}, {"input": 135, "interpolation": "LINEAR", "output": 136}, {"input": 137, "interpolation": "LINEAR", "output": 138}, {"input": 139, "interpolation": "LINEAR", "output": 140}, {"input": 141, "interpolation": "LINEAR", "output": 142}, {"input": 143, "interpolation": "LINEAR", "output": 144}, {"input": 145, "interpolation": "LINEAR", "output": 146}, {"input": 147, "interpolation": "LINEAR", "output": 148}, {"input": 149, "interpolation": "LINEAR", "output": 150}, {"input": 151, "interpolation": "LINEAR", "output": 152}, {"input": 153, "interpolation": "LINEAR", "output": 154}, {"input": 155, "interpolation": "LINEAR", "output": 156}, {"input": 157, "interpolation": "LINEAR", "output": 158}, {"input": 159, "interpolation": "LINEAR", "output": 160}, {"input": 161, "interpolation": "LINEAR", "output": 162}, {"input": 163, "interpolation": "LINEAR", "output": 164}, {"input": 165, "interpolation": "LINEAR", "output": 166}, {"input": 167, "interpolation": "LINEAR", "output": 168}, {"input": 169, "interpolation": "LINEAR", "output": 170}, {"input": 171, "interpolation": "LINEAR", "output": 172}, {"input": 173, "interpolation": "LINEAR", "output": 174}, {"input": 175, "interpolation": "LINEAR", "output": 176}, {"input": 177, "interpolation": "LINEAR", "output": 178}, {"input": 179, "interpolation": "LINEAR", "output": 180}, {"input": 181, "interpolation": "LINEAR", "output": 182}, {"input": 183, "interpolation": "LINEAR", "output": 184}, {"input": 185, "interpolation": "LINEAR", "output": 186}, {"input": 187, "interpolation": "LINEAR", "output": 188}, {"input": 189, "interpolation": "LINEAR", "output": 190}, {"input": 191, "interpolation": "LINEAR", "output": 192}, {"input": 193, "interpolation": "LINEAR", "output": 194}, {"input": 195, "interpolation": "LINEAR", "output": 196}, {"input": 197, "interpolation": "LINEAR", "output": 198}, {"input": 199, "interpolation": "LINEAR", "output": 200}, {"input": 201, "interpolation": "LINEAR", "output": 202}, {"input": 203, "interpolation": "LINEAR", "output": 204}, {"input": 205, "interpolation": "LINEAR", "output": 206}, {"input": 207, "interpolation": "LINEAR", "output": 208}, {"input": 209, "interpolation": "LINEAR", "output": 210}, {"input": 211, "interpolation": "LINEAR", "output": 212}, {"input": 213, "interpolation": "LINEAR", "output": 214}, {"input": 215, "interpolation": "LINEAR", "output": 216}, {"input": 217, "interpolation": "LINEAR", "output": 218}, {"input": 219, "interpolation": "LINEAR", "output": 220}, {"input": 221, "interpolation": "LINEAR", "output": 222}, {"input": 223, "interpolation": "LINEAR", "output": 224}, {"input": 225, "interpolation": "LINEAR", "output": 226}, {"input": 227, "interpolation": "LINEAR", "output": 228}, {"input": 229, "interpolation": "LINEAR", "output": 230}, {"input": 231, "interpolation": "LINEAR", "output": 232}, {"input": 233, "interpolation": "LINEAR", "output": 234}, {"input": 235, "interpolation": "LINEAR", "output": 236}, {"input": 237, "interpolation": "LINEAR", "output": 238}, {"input": 239, "interpolation": "LINEAR", "output": 240}, {"input": 241, "interpolation": "LINEAR", "output": 242}, {"input": 243, "interpolation": "LINEAR", "output": 244}, {"input": 245, "interpolation": "LINEAR", "output": 246}, {"input": 247, "interpolation": "LINEAR", "output": 248}, {"input": 249, "interpolation": "LINEAR", "output": 250}, {"input": 251, "interpolation": "LINEAR", "output": 252}, {"input": 253, "interpolation": "LINEAR", "output": 254}, {"input": 255, "interpolation": "LINEAR", "output": 256}, {"input": 257, "interpolation": "LINEAR", "output": 258}]}], "asset": {"extras": {"author": "FourthGreen (https://sketchfab.com/FourthGreen)", "license": "CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)", "source": "https://sketchfab.com/3d-models/rabbit-rigged-e7213589744d436b9d96e2dbb31198a5", "title": "<PERSON> Rigged"}, "generator": "Sketchfab-12.66.0", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 12776, "byteStride": 8, "name": "shortBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 30936, "byteOffset": 12776, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 12776, "byteOffset": 43712, "byteStride": 8, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 38328, "byteOffset": 56488, "byteStride": 12, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 25552, "byteOffset": 94816, "byteStride": 16, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 3072, "byteOffset": 120368, "name": "floatBufferViews"}, {"buffer": 0, "byteLength": 7008, "byteOffset": 123440, "name": "floatBufferViews"}, {"buffer": 0, "byteLength": 13632, "byteOffset": 130448, "byteStride": 12, "name": "floatBufferViews"}, {"buffer": 0, "byteLength": 9856, "byteOffset": 144080, "byteStride": 16, "name": "floatBufferViews"}], "buffers": [{"byteLength": 153936, "uri": "scene.bin"}], "images": [{"uri": "textures/Main_baseColor.png"}], "materials": [{"doubleSided": true, "name": "Main", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.8929713596497552}}], "meshes": [{"name": "Rabbit_Main_0", "primitives": [{"attributes": {"JOINTS_0": 5, "NORMAL": 1, "POSITION": 0, "TEXCOORD_0": 2, "TEXCOORD_1": 2, "WEIGHTS_0": 6}, "indices": 3, "material": 0, "mode": 4}]}], "nodes": [{"children": [1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, -1.0, 0.0, 0.0, 1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sketchfab_model"}, {"children": [2], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Rabbit.FBX"}, {"children": [3], "name": "Object_2"}, {"children": [4], "name": "RootNode"}, {"children": [5, 7, 6, 55], "name": "Object_4"}, {"children": [8], "name": "_rootJoint"}, {"matrix": [1.0, 1.232595164407831e-32, 6.938893903907228e-18, 0.0, 6.938893903907228e-18, 6.123233995736766e-17, -1.0, 0.0, -1.2750836354530465e-32, 1.0000000000000002, 6.123233995736767e-17, 0.0, 0.0, -8.881784197001252e-16, -4.440892098500626e-16, 1.0], "name": "Object_6"}, {"mesh": 0, "name": "Object_7", "skin": 0}, {"children": [9, 51, 52, 53, 54], "name": "FourthGreen_01", "rotation": [-1.7881393432617188e-07, -0.0, 0.0, 1.0], "scale": [1.0, 0.9999998211860657, 0.9999998211860657]}, {"children": [10, 15, 18, 46], "name": "Root_M_02", "rotation": [0.7070965766906738, -0.003798530902713537, 0.7070965766906738, -0.003798530902713537], "scale": [0.9999999403953552, 0.9999999403953552, 1.0], "translation": [5.252174749666638e-15, 10.403085708618164, 0.1456584632396698]}, {"children": [11], "name": "backHip_R_03", "rotation": [0.755706250667572, 0.6472363471984863, -0.0942801907658577, -0.03323260694742203], "scale": [1.0000001192092896, 0.9999998807907104, 0.9999998807907104], "translation": [-1.1963599920272827, 3.1197214126586914, -3.1191670894622803]}, {"children": [12], "name": "backKnee_R_04", "rotation": [1.0602582145580186e-09, 5.793990176528041e-09, -0.48182907700538635, 0.8762653470039368], "scale": [1.0000001192092896, 1.0000001192092896, 1.0], "translation": [3.9782462120056152, -5.960464477539063e-08, -1.1920928955078125e-06]}, {"children": [13], "name": "backAnkle_R_05", "rotation": [0.41014134883880615, 0.9098923802375793, -0.0185115784406662, -0.05947505310177803], "scale": [1.000000238418579, 1.0, 1.000000238418579], "translation": [3.8914952278137207, -4.76837158203125e-07, 2.384185791015625e-07]}, {"children": [14], "name": "backToes_R_06", "rotation": [0.7505371570587158, 0.6606553196907043, 0.011341976001858711, 0.009994865395128727], "scale": [1.0000001192092896, 0.9999999403953552, 1.000000238418579], "translation": [2.8694074153900146, -2.086162567138672e-07, -4.76837158203125e-07]}, {"name": "backToes1_R_07", "rotation": [1.148667359146538e-11, -1.4027222956229875e-22, -1.1368683772161603e-13, 1.0], "scale": [1.0, 0.9999999403953552, 0.9999999403953552], "translation": [0.5600751638412476, 2.630505323410034, 4.76837158203125e-07]}, {"children": [16], "name": "Tail0_M_08", "rotation": [-0.18721303343772888, 0.9823192954063416, -1.7259936156092254e-13, -0.00024409688194282353], "scale": [1.0, 0.9999999403953552, 1.0], "translation": [-5.082709789276123, 4.322815418243408, -2.384185791015625e-07]}, {"children": [17], "name": "Tail1_M_09", "rotation": [1.0025549834757896e-11, -1.1195293581067745e-08, -0.4341470003128052, 0.9008420705795288], "translation": [0.9856815338134766, -2.384185791015625e-07, 2.3283064365386963e-10]}, {"name": "Tail2_M_010", "rotation": [-2.897409395741235e-11, -1.3019185729490346e-08, 0.017474818974733353, 0.999847412109375], "scale": [0.9999998807907104, 0.9999998807907104, 1.0], "translation": [1.3975820541381836, -4.440892098500626e-16, -4.656612873077393e-10]}, {"children": [19], "name": "Spine1_M_011", "rotation": [-8.148908140880145e-33, 2.5734447627239542e-33, -0.10718381404876709, 0.9942392110824585], "translation": [1.5796537399291992, 0.0, -1.8189894035458565e-12]}, {"children": [20], "name": "Spine2_M_012", "rotation": [-5.06691085641023e-16, 1.4201817720687748e-14, 0.03565521910786629, 0.9993641376495361], "scale": [1.0000001192092896, 1.0000001192092896, 1.0], "translation": [2.089163303375244, -1.7763568394002505e-15, -2.0194839173657902e-28]}, {"children": [21, 27, 40], "name": "Chest_M_013", "rotation": [6.761291423658736e-14, 4.348381531693764e-14, -0.21224182844161987, 0.9772171974182129], "translation": [2.9497556686401367, -9.5367431640625e-07, 2.0194839173657902e-28]}, {"children": [22], "name": "frontRump_R_014", "rotation": [-0.24411192536354065, -0.07015098631381989, 0.6133610010147095, 0.7478479146957397], "scale": [0.9999998807907104, 1.0, 1.0000001192092896], "translation": [-0.15029430389404297, 1.743455410003662, -1.2537815570831299]}, {"children": [23], "name": "frontHip_R_015", "rotation": [0.16393744945526123, -0.24572335183620453, 0.407128244638443, 0.864286482334137], "scale": [0.9999999403953552, 1.0000001192092896, 0.9999999403953552], "translation": [2.528493881225586, 1.7763568394002505e-15, 0.0]}, {"children": [24], "name": "frontKnee_R_00", "rotation": [6.243848771347871e-10, 1.1763842344691966e-08, -0.3660654127597809, 0.9305891394615173], "scale": [1.0000001192092896, 1.0, 0.9999999403953552], "translation": [2.437546730041504, -9.5367431640625e-07, 4.76837158203125e-07]}, {"children": [25], "name": "frontAnkle_R_016", "rotation": [0.8825130462646484, 0.468246191740036, -0.04284977912902832, 0.008953642100095749], "scale": [0.9999999403953552, 1.0000004768371582, 1.000000238418579], "translation": [2.2633094787597656, -1.7763568394002505e-15, 2.220446049250313e-16]}, {"children": [26], "name": "frontToes_R_017", "rotation": [-2.328305881427184e-10, -1.734723475976807e-18, -4.0389668717666083e-28, 1.0], "scale": [1.0, 1.000000238418579, 1.0], "translation": [1.3255348205566406, 3.319888114929199, -5.960464477539062e-07]}, {"name": "frontToes1_R_018", "rotation": [2.3283059924494864e-09, 9.313225191043273e-10, -1.4901161193847656e-08, 1.0], "scale": [1.0, 1.000000238418579, 1.0000001192092896]}, {"children": [28], "name": "Neck_M_019", "rotation": [1.2897618221910725e-14, -2.9802372125686816e-08, 0.04933391883969307, 0.9987823367118835], "translation": [2.994600296020508, -1.9073486328125e-06, -5.960282578598708e-08]}, {"children": [29, 31, 35, 36], "name": "Head_M_020", "rotation": [4.917096731952425e-14, -6.359688708816591e-14, -0.12689171731472015, 0.9919165968894958], "scale": [1.0000001192092896, 1.0000001192092896, 1.0], "translation": [2.0321388244628906, -4.76837158203125e-07, 5.960464477539063e-08]}, {"children": [30], "name": "Nose_M_021", "rotation": [-9.534008375529715e-14, -1.490117718105921e-08, 0.6966384649276733, 0.717422366142273], "scale": [0.9999999403953552, 0.9999999403953552, 1.0], "translation": [2.6668853759765625, 4.665968894958496, -1.0587911840678754e-22]}, {"name": "NoseEnd_M_022", "rotation": [-2.1073446987429634e-08, 3.1401883046969226e-16, -1.4901161193847656e-08, 1.0], "translation": [0.7356290817260742, -3.552713678800501e-15, 5.960646376479417e-08]}, {"children": [32], "name": "Ear_R_023", "rotation": [-0.5829958319664001, 0.5120536088943481, -0.3620947003364563, 0.5165311098098755], "scale": [0.9999999403953552, 1.0000001192092896, 0.9999999403953552], "translation": [0.7918472290039062, -0.07079219818115234, -2.8079183101654053]}, {"children": [33], "name": "Ear1_R_024", "rotation": [-0.010817051865160465, -0.035872623324394226, 0.06502848863601685, 0.9971797466278076], "scale": [0.9999999403953552, 1.0, 1.0], "translation": [2.502199172973633, 5.960464477539062e-07, -1.9073486328125e-06]}, {"children": [34], "name": "Ear2_R_025", "rotation": [-0.008874061517417431, -0.023420898243784904, 0.02240387164056301, 0.9994352459907532], "scale": [1.0, 1.000000238418579, 1.0000001192092896], "translation": [2.4355506896972656, -4.76837158203125e-07, -1.9073486328125e-06]}, {"name": "EarEnd_R_026", "rotation": [-2.9802318834981634e-08, -8.32667162589749e-16, 2.9802322387695312e-08, 1.0], "scale": [1.0, 1.0000001192092896, 1.0000001192092896], "translation": [1.8285436630249023, 1.430511474609375e-06, -9.5367431640625e-07]}, {"name": "HeadEnd_M_027", "rotation": [-2.980243962724671e-08, -5.684342563707159e-14, -2.775727299024587e-17, 1.0], "scale": [0.9999999403953552, 0.9999999403953552, 1.0], "translation": [3.748279571533203, -9.5367431640625e-07, 1.1920565157197416e-07]}, {"children": [37], "name": "Ear_L_028", "rotation": [0.5120536684989929, 0.5829958915710449, -0.5165311098098755, -0.3620947599411011], "scale": [1.0, 1.0000001192092896, 1.0000001192092896], "translation": [0.7918472290039062, -0.07079219818115234, 2.8079187870025635]}, {"children": [38], "name": "Ear1_L_029", "rotation": [-0.010817050002515316, -0.03587263077497482, 0.06502849608659744, 0.9971797466278076], "scale": [1.0000001192092896, 1.0000001192092896, 1.000000238418579], "translation": [-2.5022006034851074, 2.384185791015625e-07, 1.9073486328125e-06]}, {"children": [39], "name": "Ear2_L_030", "rotation": [-0.008874046616256237, -0.02342088334262371, 0.022403931245207787, 0.9994352459907532], "scale": [1.0, 1.0, 0.9999999403953552], "translation": [-2.435551643371582, 9.5367431640625e-07, 1.9073486328125e-06]}, {"name": "EarEnd_L_031", "rotation": [-4.579668917787587e-16, -2.9802318834981634e-08, -1.4901159417490817e-08, 1.0], "scale": [1.0000001192092896, 1.0000001192092896, 0.9999999403953552], "translation": [-1.8285436630249023, -4.76837158203125e-07, 9.5367431640625e-07]}, {"children": [41], "name": "frontRump_L_032", "rotation": [-0.10134170949459076, 0.23251380026340485, -0.6386401057243347, 0.7265025973320007], "scale": [1.0, 0.9999999403953552, 0.9999999403953552], "translation": [-0.15029430389404297, 1.743455410003662, 1.2537816762924194]}, {"children": [42], "name": "frontHip_L_033", "rotation": [0.15444275736808777, -0.24494698643684387, 0.3661908805370331, 0.8843374252319336], "scale": [0.9999999403953552, 1.0, 1.0000001192092896], "translation": [-2.5284934043884277, -9.5367431640625e-07, 0.0]}, {"children": [43], "name": "frontKnee_L_034", "rotation": [-2.1794640758798778e-08, -5.548006498656832e-10, -0.31771689653396606, 0.9481856226921082], "scale": [1.0000001192092896, 1.0, 0.9999999403953552], "translation": [-2.437546730041504, -9.5367431640625e-07, 2.384185791015625e-07]}, {"children": [44], "name": "frontAnkle_L_035", "rotation": [0.9191866517066956, 0.3871917724609375, -0.036477092653512955, -0.062031302601099014], "scale": [0.9999999403953552, 1.0000001192092896, 1.0000001192092896], "translation": [-2.263310432434082, 0.0, 2.384185791015625e-07]}, {"children": [45], "name": "frontToes_L_036", "rotation": [4.857223416629345e-17, -2.7939670577126208e-09, 1.4901157641133977e-08, 1.0], "scale": [1.000000238418579, 1.0000003576278687, 1.0000001192092896], "translation": [-1.325535774230957, -3.3198904991149902, -2.980232238769531e-07]}, {"name": "frontToes1_L_037", "rotation": [-2.7939670577126208e-09, -9.313224857976365e-09, 4.470348002882929e-08, 1.0], "scale": [1.0000001192092896, 1.0000001192092896, 1.0]}, {"children": [47], "name": "backHip_L_038", "rotation": [-0.6471502184867859, 0.7556110620498657, -0.03368711471557617, 0.09546578675508499], "scale": [1.0, 1.000000238418579, 1.0], "translation": [-1.1963597536087036, 3.1197218894958496, 3.119166851043701]}, {"children": [48], "name": "backKnee_L_039", "rotation": [6.361546400768248e-09, 3.476393573009773e-08, -0.48182904720306396, 0.8762652277946472], "scale": [1.000000238418579, 1.0, 1.0], "translation": [-3.978245973587036, 3.3306690738754696e-16, 7.152557373046875e-07]}, {"children": [49], "name": "backAnkle_L_040", "rotation": [0.4102371037006378, 0.9098315238952637, -0.01891433075070381, -0.05961871147155762], "scale": [0.9999999403953552, 1.0, 1.000000238418579], "translation": [-3.8914952278137207, -4.76837158203125e-07, -4.76837158203125e-07]}, {"children": [50], "name": "backToes_L_041", "rotation": [0.7505372166633606, 0.6606554388999939, 0.011341981589794159, 0.009991907514631748], "scale": [0.9999998211860657, 0.9999998807907104, 1.0000001192092896], "translation": [-2.8694064617156982, -2.086162567138672e-07, 0.0]}, {"name": "backToes1_L_042", "rotation": [9.401368572525826e-13, -4.764560328305439e-22, -1.0587911840678754e-22, 1.0], "translation": [-0.5600753426551819, -2.630505323410034, -9.5367431640625e-07]}, {"name": "Scapula_L_043", "rotation": [0.15378732979297638, 0.5781810879707336, 0.543084442615509, 0.5891649723052979], "scale": [0.9999997615814209, 0.9999998807907104, 0.9999999403953552], "translation": [1.2537816762924194, 7.611302375793457, 2.864226818084717]}, {"name": "Scapula_R_044", "rotation": [0.5891650319099426, -0.5430843830108643, 0.5781810879707336, -0.1537872701883316], "scale": [0.9999998807907104, 0.9999999403953552, 0.9999999403953552], "translation": [-1.2537816762924194, 7.611302375793457, 2.864226818084717]}, {"name": "Shoulder_L_045", "rotation": [-0.4671051800251007, -0.5246973037719727, 0.4325920343399048, 0.5651280283927917], "scale": [0.9999999403953552, 1.0, 1.000000238418579], "translation": [3.0, 0.9580153822898865, 3.5885298252105713]}, {"name": "Shoulder_R_046", "rotation": [0.5651280283927917, -0.43259197473526, -0.5246972441673279, 0.46710526943206787], "scale": [0.9999999403953552, 0.9999998807907104, 1.0], "translation": [-3.0, 0.9580153822898865, 3.5885298252105713]}, {"name": "Rabbit", "rotation": [-0.7071068286895752, -0.0, 0.0, 0.7071068286895752]}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}], "skins": [{"inverseBindMatrices": 4, "joints": [5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], "skeleton": 5}], "textures": [{"sampler": 0, "source": 0}]}