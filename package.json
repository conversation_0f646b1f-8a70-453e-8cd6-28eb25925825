{"name": "expo-starter-kit-typescript", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@reactvision/react-viro": "^2.43.1", "expo": "^52.0.36", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.76.9", "react-native-permissions": "^5.3.0", "react-native-tts": "^4.1.1"}, "devDependencies": {"@babel/core": "^7.24.0", "@react-native-community/cli": "^15.1.3", "@types/crypto-js": "^4", "@types/react": "~18.3.12", "@types/react-native-svg-charts": "^5", "@types/react-native-vector-icons": "^6.4.18", "@types/uuid": "^9.0.7", "react-native-dotenv": "^3.4.11", "typescript": "~5.3.3"}, "private": true}