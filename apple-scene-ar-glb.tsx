import React, { useCallback } from "react";
import {
  ViroARScene,
  ViroARPlane,
  Viro3DObject,
  ViroAmbientLight,
  ViroARImageMarker,
  ViroAnchorFoundMap,
  ViroDirectionalLight,
  ViroARSceneNavigator,
  ViroARTrackingTargets,
} from "@reactvision/react-viro";
import { Platform } from "react-native";

ViroARTrackingTargets.createTargets({
  logo: {
    source: require("./res/apple-trigger-1.png"),
    orientation: "Up",
    physicalWidth: 0.1, // meters
    android: {
      type: "Image",
    },
  },
});

type ARSceneProps = {
  arNavigatorRef: React.RefObject<ViroARSceneNavigator>;
  onFoundAnchor: (anchorFoundMap: ViroAnchorFoundMap) => void;
};

export const AppleSceneAR_GLB: React.FC<ARSceneProps> = ({ onFoundAnchor }) => {
  const _onAnchorFound = useCallback(
    async (anchorFoundMap: ViroAnchorFoundMap) => {
      onFoundAnchor(anchorFoundMap);
    },
    []
  );
  return (
    <ViroARScene>
      <ViroAmbientLight color="#ffffff" intensity={300} />

      <ViroARImageMarker target="logo" onAnchorFound={_onAnchorFound}>
        <Viro3DObject
          type="GLB"
          position={[0, 0, -0.1]}
          scale={
            Platform.OS === "android" ? [0.005, 0.005, 0.005] : [0.1, 0.1, 0.1]
          }
          source={
            Platform.OS === "android"
              ? { uri: "file:///android_asset/apple_10_time_smaller.glb" }
              : require("./res/apple_5_time_smaller.glb")
          }
        />
      </ViroARImageMarker>
    </ViroARScene>
  );
};
