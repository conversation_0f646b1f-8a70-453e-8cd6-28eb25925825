import React, { useState, useCallback } from "react";
import {
  ViroARScene,
  ViroMaterials,
  Viro3DObject,
  ViroLightingEnvironment,
  ViroARImageMarker,
  ViroARTrackingTargets,
  ViroSpotLight,
  ViroQuad,
  ViroARSceneNavigator,
  ViroAnchorFoundMap,
} from "@reactvision/react-viro";

type ARSceneProps = {
  arNavigatorRef: React.RefObject<ViroARSceneNavigator>;
  onFoundAnchor: (anchorFoundMap: ViroAnchorFoundMap) => void;
};

// ✅ Register tracking targets
ViroARTrackingTargets.createTargets({
  logo: {
    source: require("./res/dog/dog-trigger-white.png"),
    orientation: "Up",
    physicalWidth: 0.1, // meters
    android: {
      type: "Image",
    },
  },
});

// ✅ Register materials
ViroMaterials.createMaterials({
  white: {
    lightingModel: "PBR",
    diffuseTexture: require("./res/dog-v2/base_texture.png"),
    metalnessTexture: require("./res/dog-v2/normal_map.png"),
    roughnessTexture: require("./res/dog-v2/roughness.png"),
  },
});

export const DogSceneAR: React.FC<ARSceneProps> = ({ onFoundAnchor }) => {
  const _onAnchorFound = useCallback(
    async (anchorFoundMap: ViroAnchorFoundMap) => {
      onFoundAnchor(anchorFoundMap);
    },
    []
  );

  return (
    <ViroARScene>
      <ViroLightingEnvironment source={require("./res/dog/lighting.hdr")} />

      <ViroARImageMarker
        target="logo"
        onAnchorFound={_onAnchorFound}
        onAnchorUpdated={(anchor) => console.log("Tracking updated:", anchor)}
      >
        {/* ✅ Stick model directly to marker, adjust size and alignment */}
        <Viro3DObject
          position={[0, 0, 0]}
          scale={[0.2, 0.2, 0.2]}
          source={require("./res/dog-v2/dog_obj.obj")}
          resources={[require("./res/dog-v2/dog_mtl.mtl")]}
          type="OBJ"
          materials="white"
        />

        {/* ✅ Soft spotlight for realism */}
        <ViroSpotLight
          innerAngle={5}
          outerAngle={25}
          direction={[0, -1, 0]}
          position={[0, 5, 1]}
          color="#ffffff"
          castsShadow
          shadowMapSize={2048}
          shadowNearZ={2}
          shadowFarZ={7}
          shadowOpacity={0.7}
        />

        {/* ✅ Adjust shadow receiver to sit properly under model */}
        <ViroQuad
          rotation={[-90, 0, 0]}
          position={[0, -0.01, 0]} // Slightly lower to avoid z-fighting
          width={2.5}
          height={2.5}
          arShadowReceiver
        />
      </ViroARImageMarker>
    </ViroARScene>
  );
};
